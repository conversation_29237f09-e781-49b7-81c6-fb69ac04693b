<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Tenant;
use App\Models\Process;
use App\Models\ProcessGroup;
use App\Models\User;
use App\Http\Controllers\Workflow\WorkflowController;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;
use Mockery;

class WorkflowTenantTest extends TestCase
{
    use RefreshDatabase;

    protected $tenant;
    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Tạo tenant test
        $this->tenant = Tenant::create([
            'name' => 'Test Tenant',
            'domain_name' => 'test.local',
            'start_date' => Carbon::now()->subDays(1),
            'end_date' => Carbon::now()->addDays(30),
            'is_active' => true,
            'create_by' => '********-0000-0000-0000-************',
        ]);

        // Tạo user test
        $this->user = User::create([
            'account_name' => 'testuser',
            'full_name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'tenant_id' => $this->tenant->id,
            'create_by' => '********-0000-0000-0000-************',
        ]);

        // Mock app singleton để giả lập middleware đã chạy
        app()->singleton('tenant', function () {
            return $this->tenant;
        });
    }

    public function test_workflow_data_structure_does_not_include_hardcoded_tenant()
    {
        // Tạo ProcessGroup trước
        $processGroup = ProcessGroup::create([
            'name' => 'Test Process Group',
            'is_active' => true,
            'create_by' => $this->user->id,
        ]);

        // Test data structure không chứa hardcode tenant_id
        $workflow = [
            'name' => 'Test Workflow',
            'description' => 'Test Description',
            'process_group' => ['value' => $processGroup->id]
        ];

        // Giả lập data structure mà controller sẽ tạo
        $expectedData = [
            'name' => $workflow['name'],
            'description' => $workflow['description'],
            'process_group_id' => $workflow['process_group']['value'],
            'status' => 'active',
            'create_by' => $this->user->id,
        ];

        // Kiểm tra không có tenant_id hardcode
        $this->assertArrayNotHasKey('tenant_id', $expectedData);
        $this->assertArrayHasKey('name', $expectedData);
        $this->assertArrayHasKey('status', $expectedData);
        $this->assertArrayHasKey('create_by', $expectedData);
    }

    public function test_process_model_automatically_gets_tenant_id()
    {
        // Tạo ProcessGroup trước
        $processGroup = ProcessGroup::create([
            'name' => 'Test Process Group',
            'is_active' => true,
            'create_by' => $this->user->id,
        ]);

        // Tạo Process mà không chỉ định tenant_id
        $process = Process::create([
            'name' => 'Test Process',
            'description' => 'Test Description',
            'process_group_id' => $processGroup->id,
            'status' => 'active',
            'create_by' => $this->user->id,
        ]);

        // Kiểm tra tenant_id được gán tự động
        $this->assertEquals($this->tenant->id, $process->tenant_id);
        
        // Kiểm tra process chỉ hiển thị cho tenant hiện tại
        $processes = Process::all();
        $this->assertCount(1, $processes);
        $this->assertEquals($process->id, $processes->first()->id);
    }



    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
