<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Tenant;
use App\Models\Process;
use App\Models\ProcessGroup;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Carbon\Carbon;

class TenantMiddlewareTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $tenant;
    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Tạo tenant test
        $this->tenant = Tenant::create([
            'name' => 'Test Tenant',
            'domain_name' => 'test.local',
            'start_date' => Carbon::now()->subDays(1),
            'end_date' => Carbon::now()->addDays(30),
            'is_active' => true,
            'create_by' => '********-0000-0000-0000-************',
        ]);

        // Tạo user test
        $this->user = User::create([
            'account_name' => 'testuser',
            'full_name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'tenant_id' => $this->tenant->id,
            'create_by' => '********-0000-0000-0000-************',
        ]);
    }

    public function test_tenant_middleware_identifies_tenant_correctly()
    {
        // Mock app singleton để giả lập middleware đã chạy
        app()->singleton('tenant', function () {
            return $this->tenant;
        });

        // Tạo ProcessGroup để test HasTenantTrait
        $processGroup = ProcessGroup::create([
            'name' => 'Test Process Group',
            'is_active' => true,
            'create_by' => $this->user->id,
        ]);

        // Kiểm tra tenant_id được gán tự động
        $this->assertEquals($this->tenant->id, $processGroup->tenant_id);
    }

    public function test_tenant_scope_filters_data_correctly()
    {
        // Tạo tenant khác trước
        $otherTenant = Tenant::create([
            'name' => 'Other Tenant',
            'domain_name' => 'other.local',
            'start_date' => Carbon::now()->subDays(1),
            'end_date' => Carbon::now()->addDays(30),
            'is_active' => true,
            'create_by' => '********-0000-0000-0000-************',
        ]);

        // Tạo ProcessGroup cho tenant khác (bỏ qua scope)
        $processGroup2 = ProcessGroup::withoutGlobalScopes()->create([
            'name' => 'Process Group 2',
            'is_active' => true,
            'tenant_id' => $otherTenant->id,
            'create_by' => $this->user->id,
        ]);

        // Mock app singleton cho tenant hiện tại
        app()->singleton('tenant', function () {
            return $this->tenant;
        });

        // Tạo ProcessGroup cho tenant hiện tại
        $processGroup1 = ProcessGroup::create([
            'name' => 'Process Group 1',
            'is_active' => true,
            'create_by' => $this->user->id,
        ]);

        // Kiểm tra scope chỉ trả về data của tenant hiện tại
        $processGroups = ProcessGroup::all();
        $this->assertCount(1, $processGroups);
        $this->assertEquals($processGroup1->id, $processGroups->first()->id);

        // Kiểm tra với withoutGlobalScopes sẽ trả về tất cả
        $allProcessGroups = ProcessGroup::withoutGlobalScopes()->get();
        $this->assertCount(2, $allProcessGroups);
    }

    public function test_process_inherits_tenant_from_trait()
    {
        // Mock app singleton
        app()->singleton('tenant', function () {
            return $this->tenant;
        });

        // Tạo ProcessGroup trước
        $processGroup = ProcessGroup::create([
            'name' => 'Test Process Group',
            'is_active' => true,
            'create_by' => $this->user->id,
        ]);

        // Tạo Process để test HasTenantTrait
        $process = Process::create([
            'name' => 'Test Process',
            'description' => 'Test Description',
            'process_group_id' => $processGroup->id,
            'status' => 'active',
            'create_by' => $this->user->id,
        ]);

        // Kiểm tra tenant_id được gán tự động
        $this->assertEquals($this->tenant->id, $process->tenant_id);
    }
}
